import { PropsWithChildren, RefObject } from 'react';
import { TabContextProvider } from './tab.context';

export type RefTabItem = {
  scrollToTop: () => void;
};

type Props = {
  tabName: string;
  label?: string;
  refTabItem: RefObject<RefTabItem>;
};

export type TabsWithProps = PropsWithChildren<Props>;

export const TabItem = ({ tabName, label, refTabItem, children }: TabsWithProps) => {
  return <TabContextProvider value={{ tabName, label, refTabItem }}>{children}</TabContextProvider>;
};
