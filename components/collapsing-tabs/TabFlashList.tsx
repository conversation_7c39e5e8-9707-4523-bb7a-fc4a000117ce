import { useCollapsingTabsContext } from '@/contexts/app.context';
import { useAnimateList } from './useAnimateList';
import { FlashListProps } from '@shopify/flash-list';
import { RefObject, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { ViewabilityConfig } from 'react-native';
import { FlashListAnimate } from '../FlashListAnimate';
import 'react-native-get-random-values';
import { v4 as uuid } from 'uuid';
import { useTabContext } from './tab.context';
import { scrollTo } from 'react-native-reanimated';

type Props<T> = {} & FlashListProps<T>;

export const TabFlashList = <T,>(props: Props<T>) => {
  const [listKey, setListKey] = useState(uuid());
  const { sharedProps } = useCollapsingTabsContext();
  const { refTabItem } = useTabContext();
  const { ref, scrollHandler } = useAnimateList();
  const { contentContainerStyle, scrollIndicatorInsets, scrollEventThrottle } = sharedProps;

  const viewabilityConfig = useRef<ViewabilityConfig>({
    waitForInteraction: false,
    itemVisiblePercentThreshold: 50,
    minimumViewTime: 1000,
  }).current;

  useImperativeHandle(refTabItem, () => ({
    scrollToTop: () => {
      console.log(123, ref.current);

      scrollTo(ref, 0, 0, true);
      // console.log('scrollToTop', (ref as AnimatedRef<FlashList<any>>)?.current?.scrollToIndex);
      // (ref as AnimatedRef<FlashList<any>>).current?.scrollToOffset({
      //   offset: 0,
      //   animated: true,
      // });
    },
  }));

  useEffect(() => {
    if (props.data?.length !== 0) return;
    setListKey(uuid());
  }, [props.data]);

  return (
    <FlashListAnimate
      {...props}
      flashListKey={listKey}
      ref={ref as RefObject<any>}
      onScroll={scrollHandler}
      {...(sharedProps as any)}
      contentContainerStyle={{
        ...(contentContainerStyle && typeof contentContainerStyle === 'object' ? contentContainerStyle : {}),
        ...props.contentContainerStyle,
      }}
      scrollIndicatorInsets={{ ...props.scrollIndicatorInsets, ...scrollIndicatorInsets }}
      scrollEventThrottle={scrollEventThrottle || props.scrollEventThrottle || 16}
      viewabilityConfig={viewabilityConfig}
    />
  );
};
